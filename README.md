# Busca Nome e CPF - Sistema Aprimorado

## Melhorias Implementadas

### 1. Flexibilidade na Planilha
- **Antes**: Exigia uma coluna específica chamada "Número do Processo"
- **Agora**: Usa automaticamente a primeira coluna da planilha como números de processo

### 2. Medidas Anti-Detecção
Para contornar a mensagem "Acesso predatório detectado", foram implementadas:

- **Undetected ChromeDriver**: Substitui o Selenium padrão por uma versão que evita detecção
- **User-Agents Rotativos**: Alterna entre diferentes navegadores simulados
- **Delays Aleatórios**: Pausa entre 2-5 segundos entre cada busca
- **Digitação Humana**: Simula digitação natural com pausas entre caracteres
- **Detecção de Bloqueio**: Identifica mensagens de acesso predatório e aguarda automaticamente
- **Headers Anti-Bot**: Remove propriedades que identificam automação

## Instalação

1. Execute o arquivo `instalar_dependencias.bat` ou rode manualmente:
```bash
pip install -r requirements.txt
```

2. Execute o programa:
```bash
python buscaNomeCPF.py
```

## Como Usar

1. **Prepare sua planilha**: 
   - A primeira coluna deve conter os números de processo
   - Não é necessário nomear a coluna especificamente

2. **Execute o programa**:
   - Insira suas credenciais de login
   - Selecione o arquivo Excel
   - Clique em "Iniciar Busca"

3. **Acompanhe o progresso**:
   - O sistema mostra progresso em tempo real
   - Logs de processos não encontrados
   - Salvamento automático a cada 50 registros

## Recursos Anti-Detecção

- **Delays Inteligentes**: Pausas variáveis para simular comportamento humano
- **Rotação de User-Agent**: Muda a identificação do navegador
- **Detecção Automática**: Identifica e contorna bloqueios
- **Aguardo Automático**: Pausa de 5-10 minutos quando detectado bloqueio
- **Navegação Natural**: Simula cliques e digitação humana

## Arquivos Gerados

- `Resultados_YYYYMMDD.xlsx`: Planilha com resultados completos
- `busca_nome_cpf.log`: Log detalhado das operações
- `screenshots/`: Capturas de tela em caso de erro

## Dependências

- pandas: Manipulação de planilhas
- selenium: Automação web
- undetected-chromedriver: Versão anti-detecção do ChromeDriver
- openpyxl: Leitura/escrita de arquivos Excel
