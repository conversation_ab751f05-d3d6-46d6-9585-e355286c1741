# Busca Nome e CPF - Sistema Aprimorado

## Melhorias Implementadas

### 1. Flexibilidade na Planilha
- **Antes**: Exigia uma coluna específica chamada "Número do Processo"
- **Agora**: Usa automaticamente a primeira coluna da planilha como números de processo

### 2. Medidas Anti-Detecção
Para contornar a mensagem "Acesso predatório detectado", foram implementadas:

- **Undetected ChromeDriver**: Substitui o Selenium padrão por uma versão que evita detecção
- **User-Agents Rotativos**: Alterna entre diferentes navegadores simulados
- **Delays Aleatórios**: Pausa entre 2-5 segundos entre cada busca
- **Digitação Humana**: Simula digitação natural com pausas entre caracteres
- **Detecção de Bloqueio**: Identifica mensagens de acesso predatório e aguarda automaticamente
- **Headers Anti-Bot**: Remove propriedades que identificam automação

## Instalação

1. Execute o arquivo `instalar_dependencias.bat` ou rode manualmente:
```bash
pip install -r requirements.txt
```

2. Execute o programa:
```bash
python buscaNomeCPF.py
```

## Como Usar

1. **Prepare sua planilha**: 
   - A primeira coluna deve conter os números de processo
   - Não é necessário nomear a coluna especificamente

2. **Execute o programa**:
   - Insira suas credenciais de login
   - Selecione o arquivo Excel
   - Clique em "Iniciar Busca"

3. **Acompanhe o progresso**:
   - O sistema mostra progresso em tempo real
   - Logs de processos não encontrados
   - Salvamento automático a cada 50 registros

## Recursos Anti-Detecção Otimizados

### Proteções Essenciais (Rápidas e Eficazes)
- **Undetected ChromeDriver**: Navegador stealth automático
- **User-Agents Rotativos**: 10+ navegadores diferentes a cada execução
- **Delays Inteligentes**: 2-5 segundos entre buscas (otimizado)
- **Movimento de Mouse Sutil**: Movimentos leves na página atual
- **Proteção Básica**: Scripts anti-detecção essenciais

### Contorno de Bloqueios
- **Detecção Automática**: Identifica mensagens de "acesso predatório"
- **Aguardo Razoável**: 5-10 minutos quando detectado (otimizado)
- **Retry Automático**: Recarrega e continua automaticamente
- **Logs Informativos**: Monitora todas as ações importantes

### Performance Otimizada
- **Velocidade Mantida**: Mesma velocidade da versão original
- **Funcionalidade Garantida**: XPaths e elementos testados
- **Proteção Eficiente**: Máxima proteção com mínimo impacto na velocidade

## Arquivos Gerados

- `Resultados_YYYYMMDD.xlsx`: Planilha com resultados completos
- `busca_nome_cpf.log`: Log detalhado das operações
- `screenshots/`: Capturas de tela em caso de erro

## Dependências

- pandas: Manipulação de planilhas
- selenium: Automação web
- undetected-chromedriver: Versão anti-detecção do ChromeDriver
- openpyxl: Leitura/escrita de arquivos Excel
