#!/usr/bin/env python3
"""
Monitor de Logs em Tempo Real
Monitora o arquivo de log e exibe as mensagens mais importantes
"""

import time
import os
from datetime import datetime

def monitor_logs():
    log_file = 'busca_nome_cpf.log'
    
    if not os.path.exists(log_file):
        print("Arquivo de log não encontrado. Execute o programa principal primeiro.")
        return
    
    print("=== MONITOR DE LOGS - BUSCA NOME CPF ===")
    print("Pressione Ctrl+C para sair")
    print("-" * 50)
    
    # Ir para o final do arquivo
    with open(log_file, 'r', encoding='utf-8') as f:
        f.seek(0, 2)  # Ir para o final
        
        try:
            while True:
                line = f.readline()
                if line:
                    # Filtrar mensagens importantes
                    if any(keyword in line.lower() for keyword in [
                        'predatório', 'aguardando', 'stealth', 'driver criado',
                        'erro', 'warning', 'processo', 'bloqueio'
                    ]):
                        timestamp = datetime.now().strftime('%H:%M:%S')
                        print(f"[{timestamp}] {line.strip()}")
                else:
                    time.sleep(1)
        except KeyboardInterrupt:
            print("\nMonitoramento interrompido.")

if __name__ == "__main__":
    monitor_logs()
