#!/usr/bin/env python3
"""
Script de Teste para Detecção de Modal
Testa se o sistema consegue detectar o modal de "acesso predatório"
"""

import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
import time
import random

def test_modal_detection():
    print("=== TESTE DE DETECÇÃO DE MODAL ===")
    
    # Configurar driver básico
    options = uc.ChromeOptions()
    options.add_argument("--start-maximized")
    
    try:
        driver = uc.Chrome(options=options)
        print("✅ Driver criado com sucesso")
        
        # Navegar para o site
        print("🌐 Navegando para PROJUDI...")
        driver.get("https://projudi.tjgo.jus.br/BuscaProcesso?PaginaAtual=4&TipoConsultaProcesso=24")
        time.sleep(3)
        
        # Verificar se há modal
        print("🔍 Verificando presença de modal...")
        
        modal_selectors = [
            "div[role='dialog']",
            ".ui-dialog",
            "#dialog"
        ]
        
        modal_found = False
        for selector in modal_selectors:
            try:
                modal = driver.find_element(By.CSS_SELECTOR, selector)
                if modal and modal.is_displayed():
                    print(f"⚠️  Modal encontrado com seletor: {selector}")
                    print(f"📝 Texto do modal: {modal.text}")
                    modal_found = True
                    
                    # Verificar se é o modal de acesso predatório
                    modal_text = modal.text.lower()
                    if 'acesso predatório' in modal_text or 'tente novamente' in modal_text:
                        print("🚫 MODAL DE ACESSO PREDATÓRIO DETECTADO!")
                        return True
                    break
            except:
                continue
        
        if not modal_found:
            print("✅ Nenhum modal detectado")
            
        # Verificar texto na página
        page_text = driver.page_source.lower()
        if 'acesso predatório' in page_text:
            print("🚫 TEXTO DE ACESSO PREDATÓRIO ENCONTRADO NA PÁGINA!")
            return True
        
        print("✅ Nenhum bloqueio detectado")
        return False
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        return False
    finally:
        try:
            driver.quit()
            print("🔒 Driver fechado")
        except:
            pass

if __name__ == "__main__":
    blocked = test_modal_detection()
    if blocked:
        print("\n🔴 RESULTADO: Sistema está sendo bloqueado")
        print("💡 Recomendação: Aguardar alguns minutos antes de tentar novamente")
    else:
        print("\n🟢 RESULTADO: Sistema funcionando normalmente")
        print("💡 Pode prosseguir com as buscas")
