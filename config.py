# Configurações de Delays e Comportamento Anti-Detecção

# Delays entre buscas (em segundos)
MIN_DELAY_BETWEEN_SEARCHES = 10  # Mínimo: 10 segundos
MAX_DELAY_BETWEEN_SEARCHES = 20  # Máximo: 20 segundos

# Delays quando detectado acesso predatório (em segundos)
MIN_PREDATORY_WAIT = 600   # Mínimo: 10 minutos
MAX_PREDATORY_WAIT = 1200  # Máximo: 20 minutos

# Delays de digitação (em segundos)
MIN_TYPING_DELAY = 0.1
MAX_TYPING_DELAY = 0.3
THINKING_PAUSE_CHANCE = 0.1  # 10% de chance de pausa "pensativa"
MIN_THINKING_PAUSE = 0.5
MAX_THINKING_PAUSE = 1.2

# Delays de navegação (em segundos)
MIN_PAGE_LOAD_WAIT = 4
MAX_PAGE_LOAD_WAIT = 8
MIN_CLICK_DELAY = 1.5
MAX_CLICK_DELAY = 3.0

# Configurações de comportamento humano
ENABLE_MOUSE_MOVEMENT = True
ENABLE_SCROLLING = True
ENABLE_STEALTH_SESSION = True

# Configurações de retry
MAX_RETRIES_ON_BLOCK = 3
RETRY_DELAY_MULTIPLIER = 1.5

# User-Agents adicionais (pode ser expandido)
ADDITIONAL_USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0'
]
