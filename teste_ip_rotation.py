#!/usr/bin/env python3
"""
Teste de Rotação de IP
Testa se o sistema consegue obter proxies e rotacionar IP
"""

import requests
import time
import random

def get_current_ip():
    """Obtém o IP atual"""
    try:
        response = requests.get('http://httpbin.org/ip', timeout=10)
        if response.status_code == 200:
            return response.json()['origin']
    except:
        pass
    
    try:
        response = requests.get('https://api.ipify.org', timeout=10)
        if response.status_code == 200:
            return response.text.strip()
    except:
        pass
    
    return "Não foi possível obter IP"

def get_free_proxies():
    """Obtém lista de proxies gratuitos"""
    proxies = []
    try:
        print("🔍 Buscando proxies gratuitos...")
        
        # Lista de proxies gratuitos para teste
        free_proxy_list = [
            "*******:8080",
            "*******:8080", 
            "**************:8080",
            "**************:8080"
        ]
        
        # Tentar obter proxies de APIs públicas
        try:
            response = requests.get("https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all", timeout=10)
            if response.status_code == 200:
                proxy_list = response.text.strip().split('\n')
                proxies.extend(proxy_list[:5])  # Pegar apenas os primeiros 5
                print(f"✅ {len(proxy_list)} proxies obtidos da API")
        except Exception as e:
            print(f"⚠️  Erro ao obter proxies da API: {e}")
            
        # Adicionar proxies da lista estática se não conseguiu da API
        if not proxies:
            proxies = free_proxy_list
            print("📋 Usando lista estática de proxies")
            
        return proxies
    except Exception as e:
        print(f"❌ Erro ao obter proxies: {e}")
        return []

def test_proxy(proxy):
    """Testa se um proxy está funcionando"""
    try:
        proxy_dict = {
            'http': f'http://{proxy}',
            'https': f'http://{proxy}'
        }
        response = requests.get('http://httpbin.org/ip', proxies=proxy_dict, timeout=10)
        if response.status_code == 200:
            new_ip = response.json()['origin']
            return True, new_ip
    except Exception as e:
        return False, str(e)
    return False, "Timeout ou erro"

def main():
    print("=== TESTE DE ROTAÇÃO DE IP ===")
    
    # Obter IP atual
    current_ip = get_current_ip()
    print(f"🌐 IP atual: {current_ip}")
    
    # Obter proxies
    proxies = get_free_proxies()
    print(f"📡 {len(proxies)} proxies encontrados")
    
    if not proxies:
        print("❌ Nenhum proxy disponível para teste")
        return
    
    # Testar proxies
    working_proxies = []
    print("\n🧪 Testando proxies...")
    
    for i, proxy in enumerate(proxies[:5]):  # Testar apenas os primeiros 5
        print(f"Testando {i+1}/5: {proxy}")
        is_working, result = test_proxy(proxy)
        
        if is_working:
            print(f"✅ Proxy funcionando! Novo IP: {result}")
            working_proxies.append((proxy, result))
        else:
            print(f"❌ Proxy falhou: {result}")
        
        time.sleep(1)  # Delay entre testes
    
    # Resultado final
    print(f"\n📊 RESULTADO:")
    print(f"IP original: {current_ip}")
    print(f"Proxies funcionando: {len(working_proxies)}")
    
    if working_proxies:
        print("✅ ROTAÇÃO DE IP POSSÍVEL!")
        for proxy, ip in working_proxies:
            print(f"  - {proxy} → {ip}")
    else:
        print("❌ NENHUM PROXY FUNCIONANDO")
        print("💡 Recomendação: Usar VPN ou serviço de proxy pago")

if __name__ == "__main__":
    main()
