import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import pandas as pd
import threading
from datetime import datetime
import logging
import traceback
import os
import time
import random

# Configuração do logging
logging.basicConfig(filename='busca_nome_cpf.log', level=logging.DEBUG,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# Lista expandida de User-Agents para rotação
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
]

# Headers adicionais para simular navegador real
HEADERS = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'DNT': '1',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Cache-Control': 'max-age=0'
}

def get_random_delay():
    """Retorna um delay aleatório entre 2 e 5 segundos"""
    return random.uniform(2, 5)

def get_progressive_delay(attempt_count):
    """Retorna delay progressivo baseado no número de tentativas"""
    base_delay = 2
    progressive_delay = base_delay * (1.5 ** attempt_count)
    max_delay = 30  # Máximo de 30 segundos
    return min(progressive_delay, max_delay)

def get_random_user_agent():
    """Retorna um User-Agent aleatório da lista"""
    return random.choice(USER_AGENTS)

def simulate_light_mouse_movement(driver):
    """Simula movimento leve de mouse na página atual"""
    try:
        from selenium.webdriver.common.action_chains import ActionChains
        actions = ActionChains(driver)

        # Movimento muito sutil, apenas 1-2 movimentos pequenos
        for _ in range(random.randint(1, 2)):
            x_offset = random.randint(-50, 50)
            y_offset = random.randint(-50, 50)
            actions.move_by_offset(x_offset, y_offset)
            actions.perform()
            time.sleep(random.uniform(0.1, 0.2))
    except Exception as e:
        logging.debug(f"Movimento de mouse não executado: {e}")

def add_basic_stealth(driver):
    """Adiciona proteções básicas anti-detecção"""
    try:
        # Scripts essenciais apenas
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        driver.execute_script("window.chrome = { runtime: {} }")
    except Exception as e:
        logging.debug(f"Stealth básico não aplicado: {e}")

def check_for_predatory_access_message(driver):
    """Verifica se há mensagem de acesso predatório na página"""
    try:
        # Verificar por modal específico
        modal_selectors = [
            "div[role='dialog']",
            ".ui-dialog",
            "#dialog"
        ]

        for selector in modal_selectors:
            try:
                modal = driver.find_element(By.CSS_SELECTOR, selector)
                if modal and modal.is_displayed():
                    modal_text = modal.text.lower()
                    if 'acesso predatório' in modal_text or 'tente novamente' in modal_text:
                        return True
            except:
                continue

        # Verificar texto na página
        page_text = driver.page_source.lower()
        blocked_messages = [
            'acesso predatório detectado',
            'acesso predatório detectada',
            'tente novamente em alguns minutos'
        ]

        for message in blocked_messages:
            if message in page_text:
                return True
        return False
    except Exception:
        return False

def close_modal_if_exists(driver):
    """Fecha modal de erro se existir"""
    try:
        # Tentar fechar modal clicando no botão OK
        ok_buttons = [
            "button.ui-button:contains('OK')",
            ".ui-button",
            "button[type='button']"
        ]

        for selector in ok_buttons:
            try:
                button = driver.find_element(By.CSS_SELECTOR, selector)
                if button and button.is_displayed():
                    button.click()
                    time.sleep(1)
                    return True
            except:
                continue

        # Tentar fechar clicando no X
        try:
            close_button = driver.find_element(By.CSS_SELECTOR, ".ui-dialog-titlebar-close")
            if close_button and close_button.is_displayed():
                close_button.click()
                time.sleep(1)
                return True
        except:
            pass

        # Tentar ESC
        try:
            from selenium.webdriver.common.keys import Keys
            driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
            time.sleep(1)
            return True
        except:
            pass

    except Exception as e:
        logging.debug(f"Erro ao fechar modal: {e}")

    return False

def handle_predatory_access_detection(driver):
    """Lida com detecção de acesso predatório com estratégias avançadas"""
    if check_for_predatory_access_message(driver):
        logging.warning("Detecção de acesso predatório. Implementando contorno...")

        # Fechar modal se existir
        close_modal_if_exists(driver)

        # Estratégia 1: Limpar sessão
        try:
            driver.delete_all_cookies()
            driver.execute_script("window.localStorage.clear();")
            driver.execute_script("window.sessionStorage.clear();")
            logging.info("Sessão limpa")
        except Exception as e:
            logging.debug(f"Erro ao limpar sessão: {e}")

        # Estratégia 2: Mudar User-Agent
        try:
            new_user_agent = get_random_user_agent()
            driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": new_user_agent
            })
            logging.info("User-Agent alterado")
        except Exception as e:
            logging.debug(f"Erro ao alterar User-Agent: {e}")

        # Estratégia 3: Aguardar tempo variável
        wait_time = random.uniform(300, 900)  # 5-15 minutos
        logging.warning(f"Aguardando {wait_time/60:.1f} minutos...")
        time.sleep(wait_time)

        # Estratégia 4: Navegar para página inicial e depois voltar
        try:
            driver.get("https://projudi.tjgo.jus.br/")
            time.sleep(random.uniform(3, 6))
            simulate_light_mouse_movement(driver)
            time.sleep(random.uniform(2, 4))
        except Exception as e:
            logging.warning(f"Erro na navegação: {e}")

        return True
    return False


class Application(tk.Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.master.title("Busca Nome e CPF")
        self.master.geometry("800x700")
        self.master.configure(bg="#2c3e50")
        self.excel_dir = None
        self.porcentagem_concluida = 0
        self.processo_num = "Inicializando..."
        self.last_result = "Buscando..."
        self.salvando = ""
        self.processos_nao_encontrados = 0
        self.total_processos = 0
        self.not_found_logs = []
        self.blocked_attempts = 0  # Contador de tentativas bloqueadas
        self.create_widgets()

    def create_widgets(self):
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TFrame', background="#2c3e50")
        style.configure('TButton', font=('Arial', 10, 'bold'), borderwidth=1,
                        background="#3498db", foreground="white")
        style.configure('TLabel', font=('Arial', 11),
                        background="#2c3e50", foreground="white")
        style.configure('TEntry', font=('Arial', 10),
                        fieldbackground="#34495e", foreground="white")

        main_frame = ttk.Frame(
            self.master, padding="30 30 30 30", style='TFrame')
        main_frame.grid(row=0, column=0, sticky=(
            tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(0, weight=1)
        self.master.columnconfigure(0, weight=1)
        self.master.rowconfigure(0, weight=1)

        ttk.Label(main_frame, text="Busca Nome e CPF", font=(
            'Arial', 16, 'bold')).grid(column=0, row=0, columnspan=2, pady=20)

        ttk.Label(main_frame, text="Usuário:").grid(
            column=0, row=1, sticky=tk.W, pady=5)
        self.entrada_usuario = ttk.Entry(main_frame)
        self.entrada_usuario.grid(
            column=0, row=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Senha:").grid(
            column=0, row=3, sticky=tk.W, pady=5)
        self.entrada_senha = ttk.Entry(main_frame, show="*")
        self.entrada_senha.grid(
            column=0, row=4, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(main_frame, text="Selecione a planilha (primeira coluna será usada como números de processo):").grid(
            column=0, row=5, sticky=tk.W, pady=10)
        self.excel_dir_entry = ttk.Entry(main_frame, width=50)
        self.excel_dir_entry.grid(
            column=0, row=6, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(main_frame, text="Selecionar Arquivo",
                   command=self.select_file).grid(column=1, row=6, sticky=tk.W, padx=5, pady=5)

        ttk.Button(main_frame, text="Iniciar Busca", command=self.start_progress).grid(
            column=0, row=7, sticky=(tk.W, tk.E), pady=20, columnspan=2)

    def select_file(self):
        file_path = filedialog.askopenfilename(
            filetypes=[("Excel files", "*.xlsx *.xls")])
        self.excel_dir_entry.delete(0, tk.END)
        self.excel_dir_entry.insert(0, file_path)

    def start_progress(self):
        self.excel_dir = self.excel_dir_entry.get()
        usuario = self.entrada_usuario.get()
        senha = self.entrada_senha.get()

        if not self.excel_dir:
            messagebox.showerror(
                "Erro", "Por favor, selecione um arquivo Excel.")
            return
        if not usuario or not senha:
            messagebox.showerror(
                "Erro", "Por favor, preencha o usuário e senha.")
            return

        t = threading.Thread(target=self.processar_planilha,
                             args=(usuario, senha))
        t.start()
        self.show_progress_window()

    def show_progress_window(self):
        self.progress_window = tk.Toplevel(self.master)
        self.progress_window.title("Progresso atual")
        self.progress_window.geometry("500x500")
        self.progress_window.configure(bg="#2c3e50")

        frame = ttk.Frame(self.progress_window, padding="30", style='TFrame')
        frame.grid(row=0, column=0, sticky=(
            tk.W, tk.E, tk.N, tk.S))
        frame.columnconfigure(0, weight=1)
        self.progress_window.columnconfigure(0, weight=1)
        self.progress_window.rowconfigure(0, weight=1)

        ttk.Label(frame, text="Progresso da Busca", font=(
            'Arial', 14, 'bold')).grid(column=0, row=0, pady=20)

        self.progress_bar = ttk.Progressbar(
            frame, orient="horizontal", length=400, mode="determinate", style="TProgressbar")
        self.progress_bar.grid(column=0, row=1, pady=20)

        self.progress_text = ttk.Label(frame, text="0%", font=('Arial', 12))
        self.progress_text.grid(column=0, row=2, pady=10)

        self.processo_text = ttk.Label(
            frame, text=f"Processo: {self.processo_num}", font=('Arial', 11))
        self.processo_text.grid(column=0, row=3, pady=10)

        self.last_result_text = ttk.Label(
            frame, text=f"Resultado: {self.last_result}", font=('Arial', 11))
        self.last_result_text.grid(column=0, row=4, pady=10)

        self.salvando_text = ttk.Label(frame, text="", font=('Arial', 10))
        self.salvando_text.grid(column=0, row=5, pady=10)

        self.processos_nao_encontrados_text = ttk.Label(
            frame, text="Processos não encontrados: 0", font=('Arial', 11))
        self.processos_nao_encontrados_text.grid(column=0, row=6, pady=10)

        self.total_processos_text = ttk.Label(
            frame, text="Total de processos: 0", font=('Arial', 11))
        self.total_processos_text.grid(column=0, row=7, pady=10)

        ttk.Label(frame, text="Logs:", font=('Arial', 12, 'bold')).grid(
            column=0, row=8, pady=10)
        self.log_text = tk.Text(frame, height=10, width=60)
        self.log_text.grid(column=0, row=9, pady=5)

        self.update_progress()

    def update_progress(self):
        try:
            if hasattr(self, 'progress_window') and self.progress_window.winfo_exists():
                self.progress_bar["value"] = self.porcentagem_concluida * 100
                self.progress_text["text"] = f"{round(self.porcentagem_concluida * 100, 2)}%"
                self.processo_text["text"] = f"Processo: {self.processo_num}"
                self.last_result_text["text"] = f"Resultado: {self.last_result}"
                self.salvando_text["text"] = self.salvando
                self.processos_nao_encontrados_text["text"] = f"Processos não encontrados: {self.processos_nao_encontrados}"
                self.total_processos_text["text"] = f"Total de processos: {self.total_processos}"

                if self.not_found_logs:
                    self.log_text.delete(1.0, tk.END)
                    for log in self.not_found_logs[-10:]:
                        self.log_text.insert(tk.END, log + '\n')

                if self.porcentagem_concluida < 1:
                    self.master.after(200, self.update_progress)
                else:
                    ttk.Button(self.progress_window, text="Fechar", command=self.progress_window.destroy).grid(
                        column=0, row=10, pady=20)
        except tk.TclError:
            # Janela foi fechada, parar atualizações
            pass
        except Exception as e:
            logging.error(f"Erro ao atualizar progresso: {e}")

    def extrair_dados_processo(self, driver, wait, numero_processo):
        resultados = {'Processo Encontrado': 'Não'}

        try:
            # Delay progressivo baseado em tentativas bloqueadas
            if self.blocked_attempts > 0:
                delay = get_progressive_delay(self.blocked_attempts)
                logging.info(f"Delay progressivo: {delay:.1f}s (tentativa {self.blocked_attempts})")
            else:
                delay = get_random_delay()  # 2-5 segundos normal
            time.sleep(delay)

            # Navegar até a página de busca de processos
            driver.get(
                "https://projudi.tjgo.jus.br/BuscaProcesso?PaginaAtual=4&TipoConsultaProcesso=24")

            # Delay normal após carregar a página
            time.sleep(random.uniform(1, 2))

            # Movimento sutil de mouse
            simulate_light_mouse_movement(driver)

            # Verificar se há detecção de acesso predatório ANTES de preencher
            if handle_predatory_access_detection(driver):
                self.blocked_attempts += 1
                logging.warning(f"Bloqueio detectado. Total de bloqueios: {self.blocked_attempts}")
                # Retornar sem resultado para tentar novamente
                return resultados

            # Inserir o número do processo de forma simples e funcional
            processo_input = wait.until(
                EC.presence_of_element_located((By.XPATH, '//*[@id="ProcessoNumero"]')))
            processo_input.clear()
            processo_input.send_keys(numero_processo)

            # Pequeno delay antes de clicar
            time.sleep(random.uniform(0.5, 1.0))

            # Clicar no botão para mostrar todos os processos (remover filtro de ativos)
            try:
                mostrar_todos_button = wait.until(EC.element_to_be_clickable(
                    (By.XPATH, '/html/body/div/form/div/fieldset/div[5]/fieldset/div[1]/label/button[2]')))
                mostrar_todos_button.click()
                time.sleep(random.uniform(0.3, 0.7))
            except Exception as e:
                message = f"Processo {numero_processo}: Botão para mostrar todos os processos não encontrado."
                logging.warning(message)
                self.not_found_logs.append(message)

            # Clicar no botão "Pesquisar"
            pesquisar_button = wait.until(EC.element_to_be_clickable(
                (By.XPATH, '//*[@id="divBotoesCentralizados"]/input[1]')))
            pesquisar_button.click()

            # Delay após clicar em pesquisar
            time.sleep(random.uniform(1, 3))

            # Verificar novamente se há detecção de acesso predatório após pesquisar
            if handle_predatory_access_detection(driver):
                self.blocked_attempts += 1
                message = f"Processo {numero_processo}: Acesso predatório detectado após pesquisa. Bloqueios: {self.blocked_attempts}"
                logging.warning(message)
                self.not_found_logs.append(message)
                # Retornar sem resultado para tentar novamente mais tarde
                return resultados

            # Verificar se o processo foi encontrado
            try:
                wait.until(EC.presence_of_element_located(
                    (By.XPATH, '/html/body/div[2]/form')))
                resultados['Processo Encontrado'] = 'Sim'
                # Reset contador de bloqueios em caso de sucesso
                self.blocked_attempts = 0
            except TimeoutException:
                message = f"Processo {numero_processo} não encontrado."
                logging.warning(message)
                self.not_found_logs.append(message)
                resultados['Processo Encontrado'] = 'Não'
                return resultados

            # Extrair informações do polo ativo
            try:
                nome_polo_ativo = driver.find_element(
                    By.XPATH, '/html/body/div[2]/form/div[1]/fieldset/fieldset[2]/fieldset[1]/fieldset/span[1]').text.strip()
                resultados['Nome Polo Ativo'] = nome_polo_ativo
            except Exception as e:
                message = f"Processo {numero_processo}: Nome Polo Ativo não encontrado."
                logging.warning(message)
                self.not_found_logs.append(message)
                resultados['Nome Polo Ativo'] = ''

            try:
                cpf_polo_ativo = driver.find_element(
                    By.XPATH, '/html/body/div[2]/form/div[1]/fieldset/fieldset[2]/fieldset[1]/fieldset/span[2]').text.strip()
                resultados['CPF Polo Ativo'] = cpf_polo_ativo
            except Exception as e:
                message = f"Processo {numero_processo}: CPF Polo Ativo não encontrado."
                logging.warning(message)
                self.not_found_logs.append(message)
                resultados['CPF Polo Ativo'] = ''

            return resultados

        except Exception as e:
            message = f"Erro ao processar processo {numero_processo}: {str(e)}"
            logging.error(message)
            logging.error(traceback.format_exc())
            self.not_found_logs.append(message)
            self.capture_screenshot(driver, f"erro_processo_{numero_processo}")
            return resultados

    def capture_screenshot(self, driver, filename):
        try:
            if not os.path.exists('screenshots'):
                os.makedirs('screenshots')
            driver.save_screenshot(f"screenshots/{filename}.png")
            logging.info(f"Screenshot salvo: {filename}.png")
        except Exception as e:
            logging.error(f"Erro ao capturar screenshot: {str(e)}")

    def processar_planilha(self, usuario, senha):
        try:
            df = pd.read_excel(self.excel_dir)

            # Usar a primeira coluna como números de processo
            primeira_coluna = df.columns[0]
            df = df.rename(columns={primeira_coluna: 'Número do Processo'})

            # Verificar se há dados na primeira coluna
            if df['Número do Processo'].empty:
                messagebox.showerror(
                    "Erro", "A primeira coluna da planilha está vazia.")
                return

            novas_colunas = ['Nome Polo Ativo', 'CPF Polo Ativo', 'Processo Encontrado']
            for coluna in novas_colunas:
                if coluna not in df.columns:
                    df[coluna] = ''

            # Configurar undetected-chromedriver com configurações essenciais
            options = uc.ChromeOptions()

            # User-Agent aleatório
            user_agent = get_random_user_agent()
            options.add_argument(f'--user-agent={user_agent}')

            # Configurações básicas essenciais
            options.add_argument("--start-maximized")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument("--disable-extensions")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")

            # Criar driver com undetected-chromedriver (versão simplificada)
            driver = None
            try:
                # Tentar primeiro com opções customizadas
                driver = uc.Chrome(options=options)
                logging.info("Driver criado com opções customizadas")
            except Exception as e:
                logging.error(f"Erro ao criar driver com opções customizadas: {e}")
                try:
                    # Fallback: tentar com configuração mínima
                    driver = uc.Chrome()
                    logging.info("Driver criado com configuração padrão")
                except Exception as e2:
                    logging.error(f"Erro ao criar driver padrão: {e2}")
                    messagebox.showerror("Erro", f"Não foi possível inicializar o navegador: {e2}")
                    return

            # Executar proteções básicas
            try:
                add_basic_stealth(driver)
                logging.info("Proteções básicas aplicadas")
            except Exception as e:
                logging.debug(f"Proteções básicas não aplicadas: {e}")

            wait = WebDriverWait(driver, 10)

            try:
                # Login direto
                driver.get("https://projudi.tjgo.jus.br/LogOn?PaginaAtual=-200")
                time.sleep(random.uniform(2, 3))
                wait.until(EC.presence_of_element_located(
                    (By.XPATH, '//*[@id="login"]'))).send_keys(usuario)
                driver.find_element(
                    By.XPATH, '//*[@id="senha"]').send_keys(senha)
                driver.find_element(
                    By.XPATH, '//*[@id="formLogin"]/div[4]/input[1]').click()

                # Verificar se o login foi bem-sucedido
                wait.until(EC.presence_of_element_located(
                    (By.XPATH, '//*[@id="menuPrinciapl"]/ul[2]/li')))

                total_registros = len(df)
                self.total_processos = total_registros
                for index, row in df.iterrows():
                    processo_num = str(row['Número do Processo'])
                    self.processo_num = processo_num
                    resultados = self.extrair_dados_processo(
                        driver, wait, processo_num)

                    df.at[index, 'Processo Encontrado'] = resultados.get(
                        'Processo Encontrado', 'Não')
                    if resultados.get('Processo Encontrado') == 'Sim':
                        df.at[index, 'Nome Polo Ativo'] = resultados.get(
                            'Nome Polo Ativo', '')
                        df.at[index, 'CPF Polo Ativo'] = resultados.get(
                            'CPF Polo Ativo', '')
                    else:
                        self.processos_nao_encontrados += 1

                    # Atualizar progresso
                    self.porcentagem_concluida = (index + 1) / total_registros
                    self.last_result = f"Processo {processo_num} {'encontrado' if resultados.get('Processo Encontrado') == 'Sim' else 'não encontrado'}"

                    # Salvar a cada 50 registros processados
                    if (index + 1) % 50 == 0:
                        novo_nome_arquivo = f"Resultados_{datetime.now().strftime('%Y%m%d')}.xlsx"
                        script_dir = os.path.dirname(os.path.abspath(__file__))
                        output_path = os.path.join(script_dir, novo_nome_arquivo)
                        df.to_excel(output_path, index=False)
                        self.salvando = f"Salvando... Última vez salvo: {datetime.now().strftime('%H:%M')}"
                        print(
                            f"Salvando... Última vez salvo: {datetime.now().strftime('%H:%M')}")

                # Salvar a planilha final no mesmo local do script
                novo_nome_arquivo = f"Resultados_{datetime.now().strftime('%Y%m%d')}.xlsx"
                script_dir = os.path.dirname(os.path.abspath(__file__))
                output_path = os.path.join(script_dir, novo_nome_arquivo)
                df.to_excel(output_path, index=False)
                messagebox.showinfo(
                    "Concluído", f"Processamento da planilha concluído com sucesso!\nResultados salvos em {output_path}")

            except Exception as e:
                logging.error(f"Erro durante o processamento: {str(e)}")
                logging.error(traceback.format_exc())
                messagebox.showerror(
                    "Erro", f"Ocorreu um erro durante o processamento: {str(e)}")
            finally:
                # Fechar o driver de forma segura
                try:
                    if driver:
                        driver.quit()
                        logging.info("Driver fechado com sucesso")
                except Exception as e:
                    logging.error(f"Erro ao fechar driver: {e}")

                # Salvar a planilha mesmo em caso de erro
                try:
                    novo_nome_arquivo = f"Resultados_{datetime.now().strftime('%Y%m%d')}_parcial.xlsx"
                    script_dir = os.path.dirname(os.path.abspath(__file__))
                    output_path = os.path.join(script_dir, novo_nome_arquivo)
                    df.to_excel(output_path, index=False)
                    logging.info(f"Planilha salva parcialmente em {output_path}")
                except Exception as e:
                    logging.error(f"Erro ao salvar planilha parcial: {e}")

        except Exception as e:
            logging.error(f"Erro ao processar a planilha: {str(e)}")
            logging.error(traceback.format_exc())
            messagebox.showerror(
                "Erro", f"Ocorreu um erro ao processar a planilha: {str(e)}")


if __name__ == "__main__":
    root = tk.Tk()
    app = Application(master=root)
    app.mainloop()